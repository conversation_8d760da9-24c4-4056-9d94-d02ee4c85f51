debug: false

spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ${DB_URL:*************************************}
    username: ${DB_USER:chj}
    password: ${DB_PASSWORD:123456}
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000

  # JdbcTemplate 配置（可选）
  jdbc:
    template:
      fetch-size: 100
      max-rows: 500
      query-timeout: 30s

chj:
  customer_tag: ${CUSTOMER_TAG:zdl}
  collector:
    code:
      path:
        realtime: ${COLLECTOR_CODE_PATH_REALTIME:D:\chj-data\ftp\zdl\realtime}
        backup: ${COLLECTOR_CODE_PATH_BACKUP:D:\chj-data\ftp\zdl\backup}

      update:
        interval: ${COLLECTOR_CODE_UPDATE_INTERVAL:300}

nats:
  server:
    url: ${NATS_SERVER_URL:nats://localhost:4222}
  connection:
    timeout: ${NATS_CONNECTION_TIMEOUT:5000}
  auth:
    username: ${chj.customer_tag}
    password: ${NATS_PASSWORD:123456}
  subjects:
    collectorcode:
      update:
        command: ${chj.customer_tag}.command.update.collectorcode
        status: ${chj.customer_tag}.status.update.collectorcode

custom:
  message: Hello from application.yml
  count: 5