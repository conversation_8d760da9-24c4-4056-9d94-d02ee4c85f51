package vip.cjjc.chj.server.nats.code;

//TIP To <b>Run</b> code, press <shortcut actionId="Run"/> or
// click the <icon src="AllIcons.Actions.Execute"/> icon in the gutter.
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;


@SpringBootApplication
@Component
public class Main {
    @Value("${custom.message}")
    private String message;

    @Value("${custom.count}")
    private int count;

    public static void main(String[] args) {
        SpringApplication.run(Main.class, args);
    }

    @PostConstruct
    public void init() {
        System.out.println("Message: " + message);
        System.out.println("Count: " + count);
    }
}
